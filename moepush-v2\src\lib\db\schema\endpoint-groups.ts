import { sql } from "drizzle-orm"
import { text, sqliteTable, index } from "drizzle-orm/sqlite-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"
import { z } from "zod"
import { users } from "./users"
import { endpoints } from "./endpoints"

export const endpointGroups = sqliteTable("endpoint_groups", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  status: text("status", { enum: ["active", "inactive"] }).notNull().default("active"),
  userId: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  createdAt: text("created_at").notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text("updated_at").notNull().default(sql`CURRENT_TIMESTAMP`),
}, (table) => ({
  userIdIdx: index("endpoint_groups_user_id_idx").on(table.userId),
  statusIdx: index("endpoint_groups_status_idx").on(table.status),
}))

export const endpointGroupMembers = sqliteTable("endpoint_group_members", {
  id: text("id").primaryKey(),
  groupId: text("group_id").notNull().references(() => endpointGroups.id, { onDelete: "cascade" }),
  endpointId: text("endpoint_id").notNull().references(() => endpoints.id, { onDelete: "cascade" }),
  createdAt: text("created_at").notNull().default(sql`CURRENT_TIMESTAMP`),
}, (table) => ({
  groupIdIdx: index("endpoint_group_members_group_id_idx").on(table.groupId),
  endpointIdIdx: index("endpoint_group_members_endpoint_id_idx").on(table.endpointId),
  uniqueGroupEndpoint: index("endpoint_group_members_unique_idx").on(table.groupId, table.endpointId),
}))

export const insertEndpointGroupSchema = createInsertSchema(endpointGroups).extend({
  name: z.string().min(1, "名称不能为空").max(50, "名称不能超过50个字符"),
  description: z.string().max(200, "描述不能超过200个字符").optional(),
})

export const selectEndpointGroupSchema = createSelectSchema(endpointGroups)

export type EndpointGroup = typeof endpointGroups.$inferSelect
export type NewEndpointGroup = typeof endpointGroups.$inferInsert
export type EndpointGroupMember = typeof endpointGroupMembers.$inferSelect
export type NewEndpointGroupMember = typeof endpointGroupMembers.$inferInsert
export type EndpointGroupFormData = z.infer<typeof insertEndpointGroupSchema>
