import { NextRequest, NextResponse } from "next/server"
import { getDb } from "@/lib/db"
import { endpointGroups, endpointGroupMembers, endpoints, channels } from "@/lib/db/schema"
import { pushLogs } from "@/lib/db/schema/push-logs"
import { eq } from "drizzle-orm"
import { safeInterpolate, generateId } from "@/lib/utils"
import { sendChannelMessage } from "@/lib/channels"
import { ChannelType } from "@/lib/db/schema/channels"

export const runtime = "edge"

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now()
  let groupLogId: string | null = null

  try {
    const { id } = await params
    const db = getDb()

    // 查找端点组
    const group = await db.query.endpointGroups.findFirst({
      where: eq(endpointGroups.id, id),
    })

    if (!group) {
      return NextResponse.json(
        { success: false, error: "接口组不存在" },
        { status: 404 }
      )
    }

    if (group.status !== "active") {
      return NextResponse.json(
        { success: false, error: "接口组已禁用" },
        { status: 403 }
      )
    }

    // 获取组内所有端点
    const groupMembers = await db.query.endpointGroupMembers.findMany({
      where: eq(endpointGroupMembers.groupId, id),
      with: {
        endpoint: {
          with: {
            channel: true,
          },
        },
      },
    })

    if (groupMembers.length === 0) {
      return NextResponse.json(
        { success: false, error: "接口组内没有可用的端点" },
        { status: 400 }
      )
    }

    // 获取请求体
    const body = await request.json()

    // 创建组推送日志
    groupLogId = generateId()
    await db.insert(pushLogs).values({
      id: groupLogId,
      groupId: group.id,
      userId: group.userId,
      status: "pending",
      message: JSON.stringify(body),
      createdAt: new Date().toISOString(),
    })

    const results: Array<{
      endpointId: string
      endpointName: string
      success: boolean
      error?: string
      duration?: number
    }> = []

    let successCount = 0
    let totalDuration = 0

    // 并发发送到所有端点
    const promises = groupMembers.map(async (member) => {
      const endpoint = member.endpoint
      const channel = endpoint.channel

      if (!endpoint || !channel) {
        return {
          endpointId: member.endpointId,
          endpointName: "未知端点",
          success: false,
          error: "端点或渠道不存在",
        }
      }

      if (endpoint.status !== "active" || channel.status !== "active") {
        return {
          endpointId: endpoint.id,
          endpointName: endpoint.name,
          success: false,
          error: "端点或渠道已禁用",
        }
      }

      try {
        // 处理消息模板
        const processedTemplate = safeInterpolate(endpoint.rule, { body })
        const messageObj = JSON.parse(processedTemplate)

        // 发送消息
        const result = await sendChannelMessage(
          channel.type as ChannelType,
          messageObj,
          {
            webhook: channel.webhook || undefined,
            secret: channel.secret || undefined,
            corpId: channel.corpId || undefined,
            agentId: channel.agentId || undefined,
            botToken: channel.botToken || undefined,
            chatId: channel.chatId || undefined,
          }
        )

        // 创建单独的推送日志
        await db.insert(pushLogs).values({
          id: generateId(),
          endpointId: endpoint.id,
          groupId: group.id,
          userId: group.userId,
          status: result.success ? "success" : "failed",
          message: JSON.stringify(body),
          response: result.response ? JSON.stringify(result.response) : null,
          error: result.error || null,
          duration: result.duration,
          createdAt: new Date().toISOString(),
        })

        if (result.success) {
          successCount++
        }

        if (result.duration) {
          totalDuration += result.duration
        }

        return {
          endpointId: endpoint.id,
          endpointName: endpoint.name,
          success: result.success,
          error: result.error,
          duration: result.duration,
        }
      } catch (error: any) {
        return {
          endpointId: endpoint.id,
          endpointName: endpoint.name,
          success: false,
          error: error.message || "发送失败",
        }
      }
    })

    const endpointResults = await Promise.all(promises)
    results.push(...endpointResults)

    const overallDuration = Date.now() - startTime
    const isOverallSuccess = successCount > 0

    // 更新组推送日志
    await db.update(pushLogs)
      .set({
        status: isOverallSuccess ? "success" : "failed",
        response: JSON.stringify({
          total: results.length,
          success: successCount,
          failed: results.length - successCount,
          results: results,
        }),
        error: isOverallSuccess ? null : "部分或全部端点推送失败",
        duration: overallDuration,
      })
      .where(eq(pushLogs.id, groupLogId))

    return NextResponse.json({
      success: isOverallSuccess,
      message: `推送完成，成功 ${successCount}/${results.length} 个端点`,
      total: results.length,
      successCount,
      failedCount: results.length - successCount,
      duration: overallDuration,
      results: results,
    })
  } catch (error: any) {
    const duration = Date.now() - startTime
    console.error("Group push error:", error)

    // 更新组推送日志（如果已创建）
    if (groupLogId) {
      try {
        const db = getDb()
        await db.update(pushLogs)
          .set({
            status: "failed",
            error: error.message || "未知错误",
            duration,
          })
          .where(eq(pushLogs.id, groupLogId))
      } catch (logError) {
        console.error("Failed to update group push log:", logError)
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: error.message || "组推送失败",
        duration,
      },
      { status: 500 }
    )
  }
}
