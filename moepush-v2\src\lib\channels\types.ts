export interface TemplateField {
  key: string
  description?: string
  placeholder?: string
  required?: boolean
  component?: 'input' | 'textarea' | 'checkbox' | 'select' | 'hidden'
  defaultValue?: string
  options?: Array<{value: string, label: string}>
}

export interface MessageTemplate {
  type: string
  name: string
  description: string
  fields: TemplateField[]
}

export interface ChannelConfig {
  type: string
  label: string
  description?: string
  templates: MessageTemplate[]
  configFields?: TemplateField[]
}

export interface SendMessageOptions {
  webhook?: string
  secret?: string
  corpId?: string
  agentId?: string
  botToken?: string
  chatId?: string
  [key: string]: any
}

export interface SendMessageResult {
  success: boolean
  message?: string
  error?: string
  response?: any
  duration?: number
}

export abstract class BaseChannel {
  abstract readonly config: ChannelConfig

  abstract sendMessage(message: any, options: SendMessageOptions): Promise<SendMessageResult>

  getTemplates(): MessageTemplate[] {
    return this.config.templates
  }

  getLabel(): string {
    return this.config.label
  }

  getType(): string {
    return this.config.type
  }

  getDescription(): string {
    return this.config.description || ''
  }

  getConfigFields(): TemplateField[] {
    return this.config.configFields || []
  }

  protected async measureDuration<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const start = Date.now()
    try {
      const result = await fn()
      const duration = Date.now() - start
      return { result, duration }
    } catch (error) {
      const duration = Date.now() - start
      throw { error, duration }
    }
  }
}
