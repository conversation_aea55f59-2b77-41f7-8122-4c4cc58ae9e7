import { sql } from "drizzle-orm"
import { text, sqliteTable, index } from "drizzle-orm/sqlite-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"
import { z } from "zod"
import { users } from "./users"
import { channels } from "./channels"

export const endpoints = sqliteTable("endpoints", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  rule: text("rule").notNull(), // JSON template for message formatting
  channelId: text("channel_id").notNull().references(() => channels.id, { onDelete: "cascade" }),
  status: text("status", { enum: ["active", "inactive"] }).notNull().default("active"),
  userId: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  createdAt: text("created_at").notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text("updated_at").notNull().default(sql`CURRENT_TIMESTAMP`),
}, (table) => ({
  userIdIdx: index("endpoints_user_id_idx").on(table.userId),
  channelIdIdx: index("endpoints_channel_id_idx").on(table.channelId),
  statusIdx: index("endpoints_status_idx").on(table.status),
}))

export const insertEndpointSchema = createInsertSchema(endpoints).extend({
  name: z.string().min(1, "名称不能为空").max(50, "名称不能超过50个字符"),
  description: z.string().max(200, "描述不能超过200个字符").optional(),
  rule: z.string().min(1, "消息模板不能为空"),
  channelId: z.string().min(1, "必须选择一个渠道"),
})

export const selectEndpointSchema = createSelectSchema(endpoints)

export type Endpoint = typeof endpoints.$inferSelect
export type NewEndpoint = typeof endpoints.$inferInsert
export type EndpointFormData = z.infer<typeof insertEndpointSchema>
