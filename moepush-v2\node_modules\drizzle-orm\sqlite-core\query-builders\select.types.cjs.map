{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/select.types.ts"], "sourcesContent": ["import type { ColumnsSelection, Placeholder, SQL, View } from '~/sql/sql.ts';\nimport type { SQLiteColumn } from '~/sqlite-core/columns/index.ts';\nimport type { SQLiteTable, SQLiteTableWithColumns } from '~/sqlite-core/table.ts';\nimport type { Assume, ValidateShape } from '~/utils.ts';\n\nimport type {\n\tSelectedFields as SelectFieldsBase,\n\tSelectedFieldsFlat as SelectFieldsFlatBase,\n\tSelectedFieldsOrdered as SelectFieldsOrderedBase,\n} from '~/operations.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tAppendToNullabilityMap,\n\tAppendToResult,\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tJoinNullability,\n\tJoinType,\n\tMapColumnsToTableAlias,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { Table, UpdateTableConfig } from '~/table.ts';\nimport type { SQLitePreparedQuery } from '../session.ts';\nimport type { SQLiteViewBase } from '../view-base.ts';\nimport type { SQLiteViewWithSelection } from '../view.ts';\nimport type { SQLiteSelectBase, SQLiteSelectQueryBuilderBase } from './select.ts';\n\nexport interface SQLiteSelectJoinConfig {\n\ton: SQL | undefined;\n\ttable: SQLiteTable | Subquery | SQLiteViewBase | SQL;\n\talias: string | undefined;\n\tjoinType: JoinType;\n}\n\nexport type BuildAliasTable<TTable extends SQLiteTable | View, TAlias extends string> = TTable extends Table\n\t? SQLiteTableWithColumns<\n\t\tUpdateTableConfig<TTable['_']['config'], {\n\t\t\tname: TAlias;\n\t\t\tcolumns: MapColumnsToTableAlias<TTable['_']['columns'], TAlias, 'sqlite'>;\n\t\t}>\n\t>\n\t: TTable extends View ? SQLiteViewWithSelection<\n\t\t\tTAlias,\n\t\t\tTTable['_']['existing'],\n\t\t\tMapColumnsToTableAlias<TTable['_']['selectedFields'], TAlias, 'sqlite'>\n\t\t>\n\t: never;\n\nexport interface SQLiteSelectConfig {\n\twithList?: Subquery[];\n\tfields: Record<string, unknown>;\n\tfieldsFlat?: SelectedFieldsOrdered;\n\twhere?: SQL;\n\thaving?: SQL;\n\ttable: SQLiteTable | Subquery | SQLiteViewBase | SQL;\n\tlimit?: number | Placeholder;\n\toffset?: number | Placeholder;\n\tjoins?: SQLiteSelectJoinConfig[];\n\torderBy?: (SQLiteColumn | SQL | SQL.Aliased)[];\n\tgroupBy?: (SQLiteColumn | SQL | SQL.Aliased)[];\n\tdistinct?: boolean;\n\tsetOperators: {\n\t\trightSelect: TypedQueryBuilder<any, any>;\n\t\ttype: SetOperator;\n\t\tisAll: boolean;\n\t\torderBy?: (SQLiteColumn | SQL | SQL.Aliased)[];\n\t\tlimit?: number | Placeholder;\n\t\toffset?: number | Placeholder;\n\t}[];\n}\n\nexport type SQLiteSelectJoin<\n\tT extends AnySQLiteSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTJoinedTable extends SQLiteTable | Subquery | SQLiteViewBase | SQL,\n\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n> = T extends any ? SQLiteSelectWithout<\n\t\tSQLiteSelectKind<\n\t\t\tT['_']['hkt'],\n\t\t\tT['_']['tableName'],\n\t\t\tT['_']['resultType'],\n\t\t\tT['_']['runResult'],\n\t\t\tAppendToResult<\n\t\t\t\tT['_']['tableName'],\n\t\t\t\tT['_']['selection'],\n\t\t\t\tTJoinedName,\n\t\t\t\tTJoinedTable extends SQLiteTable ? TJoinedTable['_']['columns']\n\t\t\t\t\t: TJoinedTable extends Subquery | View ? Assume<TJoinedTable['_']['selectedFields'], SelectedFields>\n\t\t\t\t\t: never,\n\t\t\t\tT['_']['selectMode']\n\t\t\t>,\n\t\t\tT['_']['selectMode'] extends 'partial' ? T['_']['selectMode'] : 'multiple',\n\t\t\tAppendToNullabilityMap<T['_']['nullabilityMap'], TJoinedName, TJoinType>,\n\t\t\tT['_']['dynamic'],\n\t\t\tT['_']['excludedMethods']\n\t\t>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>\n\t: never;\n\nexport type SQLiteSelectJoinFn<\n\tT extends AnySQLiteSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n> = 'cross' extends TJoinType ? <\n\t\tTJoinedTable extends SQLiteTable | Subquery | SQLiteViewBase | SQL,\n\t\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n\t>(table: TJoinedTable) => SQLiteSelectJoin<T, TDynamic, TJoinType, TJoinedTable, TJoinedName>\n\t: <\n\t\tTJoinedTable extends SQLiteTable | Subquery | SQLiteViewBase | SQL,\n\t\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n\t>(\n\t\ttable: TJoinedTable,\n\t\ton: ((aliases: T['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t) => SQLiteSelectJoin<T, TDynamic, TJoinType, TJoinedTable, TJoinedName>;\n\nexport type SelectedFieldsFlat = SelectFieldsFlatBase<SQLiteColumn>;\n\nexport type SelectedFields = SelectFieldsBase<SQLiteColumn, SQLiteTable>;\n\nexport type SelectedFieldsOrdered = SelectFieldsOrderedBase<SQLiteColumn>;\n\nexport interface SQLiteSelectHKTBase {\n\ttableName: string | undefined;\n\tresultType: 'sync' | 'async';\n\trunResult: unknown;\n\tselection: unknown;\n\tselectMode: SelectMode;\n\tnullabilityMap: unknown;\n\tdynamic: boolean;\n\texcludedMethods: string;\n\tresult: unknown;\n\tselectedFields: unknown;\n\t_type: unknown;\n}\n\nexport type SQLiteSelectKind<\n\tT extends SQLiteSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability>,\n\tTDynamic extends boolean,\n\tTExcludedMethods extends string,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> = (T & {\n\ttableName: TTableName;\n\tresultType: TResultType;\n\trunResult: TRunResult;\n\tselection: TSelection;\n\tselectMode: TSelectMode;\n\tnullabilityMap: TNullabilityMap;\n\tdynamic: TDynamic;\n\texcludedMethods: TExcludedMethods;\n\tresult: TResult;\n\tselectedFields: TSelectedFields;\n})['_type'];\n\nexport interface SQLiteSelectQueryBuilderHKT extends SQLiteSelectHKTBase {\n\t_type: SQLiteSelectQueryBuilderBase<\n\t\tSQLiteSelectQueryBuilderHKT,\n\t\tthis['tableName'],\n\t\tthis['resultType'],\n\t\tthis['runResult'],\n\t\tAssume<this['selection'], ColumnsSelection>,\n\t\tthis['selectMode'],\n\t\tAssume<this['nullabilityMap'], Record<string, JoinNullability>>,\n\t\tthis['dynamic'],\n\t\tthis['excludedMethods'],\n\t\tAssume<this['result'], any[]>,\n\t\tAssume<this['selectedFields'], ColumnsSelection>\n\t>;\n}\n\nexport interface SQLiteSelectHKT extends SQLiteSelectHKTBase {\n\t_type: SQLiteSelectBase<\n\t\tthis['tableName'],\n\t\tthis['resultType'],\n\t\tthis['runResult'],\n\t\tAssume<this['selection'], ColumnsSelection>,\n\t\tthis['selectMode'],\n\t\tAssume<this['nullabilityMap'], Record<string, JoinNullability>>,\n\t\tthis['dynamic'],\n\t\tthis['excludedMethods'],\n\t\tAssume<this['result'], any[]>,\n\t\tAssume<this['selectedFields'], ColumnsSelection>\n\t>;\n}\n\nexport type SQLiteSetOperatorExcludedMethods =\n\t| 'config'\n\t| 'leftJoin'\n\t| 'rightJoin'\n\t| 'innerJoin'\n\t| 'fullJoin'\n\t| 'where'\n\t| 'having'\n\t| 'groupBy';\n\nexport type CreateSQLiteSelectFromBuilderMode<\n\tTBuilderMode extends 'db' | 'qb',\n\tTTableName extends string | undefined,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n> = TBuilderMode extends 'db' ? SQLiteSelectBase<\n\t\tTTableName,\n\t\tTResultType,\n\t\tTRunResult,\n\t\tTSelection,\n\t\tTSelectMode\n\t>\n\t: SQLiteSelectQueryBuilderBase<\n\t\tSQLiteSelectQueryBuilderHKT,\n\t\tTTableName,\n\t\tTResultType,\n\t\tTRunResult,\n\t\tTSelection,\n\t\tTSelectMode\n\t>;\n\nexport type SQLiteSelectWithout<\n\tT extends AnySQLiteSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n\tTResetExcluded extends boolean = false,\n> = TDynamic extends true ? T : Omit<\n\tSQLiteSelectKind<\n\t\tT['_']['hkt'],\n\t\tT['_']['tableName'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tT['_']['selection'],\n\t\tT['_']['selectMode'],\n\t\tT['_']['nullabilityMap'],\n\t\tTDynamic,\n\t\tTResetExcluded extends true ? K : T['_']['excludedMethods'] | K,\n\t\tT['_']['result'],\n\t\tT['_']['selectedFields']\n\t>,\n\tTResetExcluded extends true ? K : T['_']['excludedMethods'] | K\n>;\n\nexport type SQLiteSelectExecute<T extends AnySQLiteSelect> = T['_']['result'];\n\nexport type SQLiteSelectPrepare<T extends AnySQLiteSelect> = SQLitePreparedQuery<\n\t{\n\t\ttype: T['_']['resultType'];\n\t\trun: T['_']['runResult'];\n\t\tall: T['_']['result'];\n\t\tget: T['_']['result'][number] | undefined;\n\t\tvalues: any[][];\n\t\texecute: SQLiteSelectExecute<T>;\n\t}\n>;\n\nexport type SQLiteSelectDynamic<T extends AnySQLiteSelectQueryBuilder> = SQLiteSelectKind<\n\tT['_']['hkt'],\n\tT['_']['tableName'],\n\tT['_']['resultType'],\n\tT['_']['runResult'],\n\tT['_']['selection'],\n\tT['_']['selectMode'],\n\tT['_']['nullabilityMap'],\n\ttrue,\n\tnever,\n\tT['_']['result'],\n\tT['_']['selectedFields']\n>;\n\nexport type SQLiteSelectQueryBuilder<\n\tTHKT extends SQLiteSelectHKTBase = SQLiteSelectQueryBuilderHKT,\n\tTTableName extends string | undefined = string | undefined,\n\tTResultType extends 'sync' | 'async' = 'sync' | 'async',\n\tTRunResult = unknown,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n\tTResult extends any[] = unknown[],\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = SQLiteSelectQueryBuilderBase<\n\tTHKT,\n\tTTableName,\n\tTResultType,\n\tTRunResult,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\ttrue,\n\tnever,\n\tTResult,\n\tTSelectedFields\n>;\n\nexport type AnySQLiteSelectQueryBuilder = SQLiteSelectQueryBuilderBase<\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany\n>;\n\nexport type AnySQLiteSetOperatorInterface = SQLiteSetOperatorInterface<any, any, any, any, any, any, any, any, any>;\n\nexport interface SQLiteSetOperatorInterface<\n\tTTableName extends string | undefined,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode = 'single',\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> {\n\t_: {\n\t\treadonly hkt: SQLiteSelectHKTBase;\n\t\treadonly tableName: TTableName;\n\t\treadonly resultType: TResultType;\n\t\treadonly runResult: TRunResult;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t};\n}\n\nexport type SQLiteSetOperatorWithResult<TResult extends any[]> = SQLiteSetOperatorInterface<\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tTResult,\n\tany\n>;\n\nexport type SQLiteSelect<\n\tTTableName extends string | undefined = string | undefined,\n\tTResultType extends 'sync' | 'async' = 'sync' | 'async',\n\tTRunResult = unknown,\n\tTSelection extends ColumnsSelection = Record<string, any>,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n> = SQLiteSelectBase<TTableName, TResultType, TRunResult, TSelection, TSelectMode, TNullabilityMap, true, never>;\n\nexport type AnySQLiteSelect = SQLiteSelectBase<any, any, any, any, any, any, any, any, any, any>;\n\nexport type SQLiteSetOperator<\n\tTTableName extends string | undefined = string | undefined,\n\tTResultType extends 'sync' | 'async' = 'sync' | 'async',\n\tTRunResult = unknown,\n\tTSelection extends ColumnsSelection = Record<string, any>,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n> = SQLiteSelectBase<\n\tTTableName,\n\tTResultType,\n\tTRunResult,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\ttrue,\n\tSQLiteSetOperatorExcludedMethods\n>;\n\nexport type SetOperatorRightSelect<\n\tTValue extends SQLiteSetOperatorWithResult<TResult>,\n\tTResult extends any[],\n> = TValue extends SQLiteSetOperatorInterface<any, any, any, any, any, any, any, any, infer TValueResult, any>\n\t? ValidateShape<\n\t\tTValueResult[number],\n\t\tTResult[number],\n\t\tTypedQueryBuilder<any, TValueResult>\n\t>\n\t: TValue;\n\nexport type SetOperatorRestSelect<\n\tTValue extends readonly SQLiteSetOperatorWithResult<TResult>[],\n\tTResult extends any[],\n> = TValue extends [infer First, ...infer Rest]\n\t? First extends SQLiteSetOperatorInterface<any, any, any, any, any, any, any, any, infer TValueResult, any>\n\t\t? Rest extends AnySQLiteSetOperatorInterface[] ? [\n\t\t\t\tValidateShape<TValueResult[number], TResult[number], TypedQueryBuilder<any, TValueResult>>,\n\t\t\t\t...SetOperatorRestSelect<Rest, TResult>,\n\t\t\t]\n\t\t: ValidateShape<TValueResult[number], TResult[number], TypedQueryBuilder<any, TValueResult>[]>\n\t: never\n\t: TValue;\n\nexport type SQLiteCreateSetOperatorFn = <\n\tTTableName extends string | undefined,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTSelection extends ColumnsSelection,\n\tTValue extends SQLiteSetOperatorWithResult<TResult>,\n\tTRest extends SQLiteSetOperatorWithResult<TResult>[],\n\tTSelectMode extends SelectMode = 'single',\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n>(\n\tleftSelect: SQLiteSetOperatorInterface<\n\t\tTTableName,\n\t\tTResultType,\n\t\tTRunResult,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\trightSelect: SetOperatorRightSelect<TValue, TResult>,\n\t...restSelects: SetOperatorRestSelect<TRest, TResult>\n) => SQLiteSelectWithout<\n\tSQLiteSelectBase<\n\t\tTTableName,\n\t\tTResultType,\n\t\tTRunResult,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tfalse,\n\tSQLiteSetOperatorExcludedMethods,\n\ttrue\n>;\n\nexport type GetSQLiteSetOperators = {\n\tunion: SQLiteCreateSetOperatorFn;\n\tintersect: SQLiteCreateSetOperatorFn;\n\texcept: SQLiteCreateSetOperatorFn;\n\tunionAll: SQLiteCreateSetOperatorFn;\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}