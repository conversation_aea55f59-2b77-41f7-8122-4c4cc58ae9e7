import { BaseChannel, SendMessageOptions, SendMessageResult } from "./types"
import { DingTalkChannel } from "./dingtalk"
import { WecomChannel } from "./wecom"
import { TelegramChannel } from "./telegram"
import { WebhookChannel } from "./webhook"
import { CHANNEL_TYPES, type ChannelType } from "../db/schema/channels"

// 注册所有渠道
const channels: Record<ChannelType, BaseChannel> = {
  [CHANNEL_TYPES.DINGTALK]: new DingTalkChannel(),
  [CHANNEL_TYPES.WECOM]: new WecomChannel(),
  [CHANNEL_TYPES.WECOM_APP]: new WecomChannel(), // 暂时复用企业微信群机器人
  [CHANNEL_TYPES.TELEGRAM]: new TelegramChannel(),
  [CHANNEL_TYPES.FEISHU]: new WebhookChannel(), // 暂时使用通用 Webhook
  [CHANNEL_TYPES.DISCORD]: new WebhookChannel(), // 暂时使用通用 Webhook
  [CHANNEL_TYPES.BARK]: new WebhookChannel(), // 暂时使用通用 Webhook
  [CHANNEL_TYPES.WEBHOOK]: new WebhookChannel(),
}

// 获取所有渠道标签
export const CHANNEL_LABELS: Record<ChannelType, string> = Object.entries(channels).reduce(
  (acc, [type, channel]) => ({
    ...acc,
    [type]: channel.getLabel(),
  }),
  {} as Record<ChannelType, string>
)

// 获取所有渠道模板
export const CHANNEL_TEMPLATES = Object.entries(channels).reduce(
  (acc, [type, channel]) => ({
    ...acc,
    [type]: channel.getTemplates(),
  }),
  {} as Record<ChannelType, any>
)

// 获取所有渠道配置字段
export const CHANNEL_CONFIG_FIELDS = Object.entries(channels).reduce(
  (acc, [type, channel]) => ({
    ...acc,
    [type]: channel.getConfigFields(),
  }),
  {} as Record<ChannelType, any>
)

// 获取指定渠道
export function getChannel(type: ChannelType): BaseChannel {
  const channel = channels[type]
  if (!channel) {
    throw new Error(`不支持的渠道类型: ${type}`)
  }
  return channel
}

// 发送消息
export async function sendChannelMessage(
  type: ChannelType,
  message: any,
  options: SendMessageOptions
): Promise<SendMessageResult> {
  const channel = getChannel(type)
  return channel.sendMessage(message, options)
}

// 获取所有可用渠道
export function getAvailableChannels() {
  return Object.entries(channels).map(([type, channel]) => ({
    type: type as ChannelType,
    label: channel.getLabel(),
    description: channel.getDescription(),
    templates: channel.getTemplates(),
    configFields: channel.getConfigFields(),
  }))
}

// 验证渠道配置
export function validateChannelConfig(type: ChannelType, config: Record<string, any>): string[] {
  const channel = getChannel(type)
  const configFields = channel.getConfigFields()
  const errors: string[] = []

  for (const field of configFields) {
    if (field.required && !config[field.key]) {
      errors.push(`${field.description || field.key} 是必填项`)
    }
  }

  return errors
}

// 导出类型和常量
export { CHANNEL_TYPES, type ChannelType }
export type { BaseChannel, SendMessageOptions, SendMessageResult }
