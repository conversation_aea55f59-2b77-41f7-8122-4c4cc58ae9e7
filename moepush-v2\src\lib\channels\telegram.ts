import { BaseChannel, ChannelConfig, SendMessageOptions, SendMessageResult } from "./types"

interface TelegramMessage {
  text?: string
  parse_mode?: 'HTML' | 'Markdown' | 'MarkdownV2'
  disable_web_page_preview?: boolean
  disable_notification?: boolean
}

export class TelegramChannel extends BaseChannel {
  readonly config: ChannelConfig = {
    type: "telegram",
    label: "Telegram 机器人",
    description: "通过 Telegram 机器人发送消息通知",
    configFields: [
      {
        key: "botToken",
        description: "Bot Token",
        placeholder: "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11",
        required: true,
        component: 'input'
      },
      {
        key: "chatId",
        description: "Chat ID",
        placeholder: "-1001234567890",
        required: true,
        component: 'input'
      }
    ],
    templates: [
      {
        type: "text",
        name: "文本消息",
        description: "发送纯文本消息",
        fields: [
          {
            key: "text",
            description: "消息内容",
            required: true,
            component: 'textarea'
          },
          {
            key: "disable_web_page_preview",
            description: "禁用网页预览",
            component: 'checkbox'
          },
          {
            key: "disable_notification",
            description: "静默发送",
            component: 'checkbox'
          }
        ]
      },
      {
        type: "markdown",
        name: "Markdown消息",
        description: "发送Markdown格式消息",
        fields: [
          {
            key: "text",
            description: "Markdown内容",
            required: true,
            component: 'textarea'
          },
          {
            key: "parse_mode",
            component: 'hidden',
            defaultValue: "Markdown"
          },
          {
            key: "disable_web_page_preview",
            description: "禁用网页预览",
            component: 'checkbox'
          },
          {
            key: "disable_notification",
            description: "静默发送",
            component: 'checkbox'
          }
        ]
      },
      {
        type: "html",
        name: "HTML消息",
        description: "发送HTML格式消息",
        fields: [
          {
            key: "text",
            description: "HTML内容",
            required: true,
            component: 'textarea'
          },
          {
            key: "parse_mode",
            component: 'hidden',
            defaultValue: "HTML"
          },
          {
            key: "disable_web_page_preview",
            description: "禁用网页预览",
            component: 'checkbox'
          },
          {
            key: "disable_notification",
            description: "静默发送",
            component: 'checkbox'
          }
        ]
      }
    ]
  }

  async sendMessage(message: TelegramMessage, options: SendMessageOptions): Promise<SendMessageResult> {
    const { botToken, chatId } = options

    if (!botToken) {
      return {
        success: false,
        error: "缺少 Bot Token"
      }
    }

    if (!chatId) {
      return {
        success: false,
        error: "缺少 Chat ID"
      }
    }

    try {
      const { result, duration } = await this.measureDuration(async () => {
        const url = `https://api.telegram.org/bot${botToken}/sendMessage`
        
        const payload = {
          chat_id: chatId,
          ...message
        }

        const response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        })

        const data = await response.json() as { ok: boolean; description?: string; result?: any }
        
        if (!data.ok) {
          throw new Error(`Telegram 消息推送失败: ${data.description || '未知错误'}`)
        }

        return data
      })

      return {
        success: true,
        message: "消息发送成功",
        response: result,
        duration
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || "发送失败",
        duration: error.duration
      }
    }
  }
}
