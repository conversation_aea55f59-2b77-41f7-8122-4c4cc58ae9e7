import { BaseChannel, ChannelConfig, SendMessageOptions, SendMessageResult } from "./types"

interface DingTalkMessage {
  msgtype: string
  text?: { content: string }
  markdown?: { title: string; text: string }
  actionCard?: {
    title: string
    text: string
    btnOrientation?: string
    singleTitle?: string
    singleURL?: string
    btns?: Array<{ title: string; actionURL: string }>
  }
  at?: {
    atMobiles?: string[]
    atUserIds?: string[]
    isAtAll?: boolean
  }
}

export class DingTalkChannel extends BaseChannel {
  readonly config: ChannelConfig = {
    type: "dingtalk",
    label: "钉钉群机器人",
    description: "通过钉钉群机器人发送消息通知",
    configFields: [
      {
        key: "webhook",
        description: "Webhook 地址",
        placeholder: "https://oapi.dingtalk.com/robot/send?access_token=xxx",
        required: true,
        component: 'input'
      },
      {
        key: "secret",
        description: "加签密钥（可选）",
        placeholder: "SEC...",
        component: 'input'
      }
    ],
    templates: [
      {
        type: "text",
        name: "文本消息",
        description: "最基础的消息类型",
        fields: [
          {
            key: "text.content",
            description: "消息内容",
            required: true,
            component: 'textarea'
          },
          {
            key: "at.atMobiles",
            description: "被@人的手机号（多个用逗号分隔）",
            component: 'input'
          },
          {
            key: "at.isAtAll",
            description: "是否@所有人",
            component: 'checkbox'
          },
          {
            key: "msgtype",
            component: 'hidden',
            defaultValue: "text"
          }
        ]
      },
      {
        type: "markdown",
        name: "Markdown消息",
        description: "支持Markdown格式的富文本消息",
        fields: [
          {
            key: "markdown.title",
            description: "首屏会话透出的展示内容",
            required: true,
            component: 'input'
          },
          {
            key: "markdown.text",
            description: "markdown格式的消息内容",
            required: true,
            component: 'textarea'
          },
          {
            key: "at.atMobiles",
            description: "被@人的手机号（多个用逗号分隔）",
            component: 'input'
          },
          {
            key: "at.isAtAll",
            description: "是否@所有人",
            component: 'checkbox'
          },
          {
            key: "msgtype",
            component: 'hidden',
            defaultValue: "markdown"
          }
        ]
      }
    ]
  }

  private async generateSign(secret: string, timestamp: number): Promise<string> {
    const encoder = new TextEncoder()
    const keyData = encoder.encode(secret)
    const msgData = encoder.encode(`${timestamp}\n${secret}`)
    
    const key = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )
    
    const signature = await crypto.subtle.sign('HMAC', key, msgData)
    return btoa(String.fromCharCode(...new Uint8Array(signature)))
  }

  async sendMessage(message: DingTalkMessage, options: SendMessageOptions): Promise<SendMessageResult> {
    const { webhook, secret } = options

    if (!webhook) {
      return {
        success: false,
        error: "缺少 Webhook 地址"
      }
    }

    try {
      const { result, duration } = await this.measureDuration(async () => {
        // 处理 at.atMobiles 字符串转数组
        if (message.at?.atMobiles && typeof message.at.atMobiles === 'string') {
          message.at.atMobiles = message.at.atMobiles.split(',').map(s => s.trim()).filter(Boolean)
        }

        let url = webhook
        if (secret) {
          const timestamp = Date.now()
          const sign = await this.generateSign(secret, timestamp)
          const urlObj = new URL(webhook)
          urlObj.searchParams.append('timestamp', timestamp.toString())
          urlObj.searchParams.append('sign', sign)
          url = urlObj.toString()
        }

        const response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(message)
        })

        const data = await response.json() as { errcode: number; errmsg: string }
        
        if (data.errcode !== 0) {
          throw new Error(`钉钉消息推送失败: ${data.errmsg}`)
        }

        return data
      })

      return {
        success: true,
        message: "消息发送成功",
        response: result,
        duration
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || "发送失败",
        duration: error.duration
      }
    }
  }
}
