import { BaseChannel, ChannelConfig, SendMessageOptions, SendMessageResult } from "./types"

interface WebhookMessage {
  [key: string]: any
}

export class WebhookChannel extends BaseChannel {
  readonly config: ChannelConfig = {
    type: "webhook",
    label: "通用 Webhook",
    description: "发送 HTTP POST 请求到指定的 Webhook 地址",
    configFields: [
      {
        key: "webhook",
        description: "Webhook 地址",
        placeholder: "https://example.com/webhook",
        required: true,
        component: 'input'
      },
      {
        key: "secret",
        description: "密钥（可选，用于签名验证）",
        component: 'input'
      }
    ],
    templates: [
      {
        type: "json",
        name: "JSON 数据",
        description: "发送自定义 JSON 数据",
        fields: [
          {
            key: "data",
            description: "JSON 数据（将作为请求体发送）",
            required: true,
            component: 'textarea',
            placeholder: '{"message": "Hello World", "level": "info"}'
          }
        ]
      },
      {
        type: "form",
        name: "表单数据",
        description: "发送表单格式数据",
        fields: [
          {
            key: "title",
            description: "标题",
            component: 'input'
          },
          {
            key: "content",
            description: "内容",
            component: 'textarea'
          },
          {
            key: "level",
            description: "级别",
            component: 'select',
            options: [
              { value: "info", label: "信息" },
              { value: "warning", label: "警告" },
              { value: "error", label: "错误" },
              { value: "success", label: "成功" }
            ],
            defaultValue: "info"
          }
        ]
      }
    ]
  }

  private async generateSignature(payload: string, secret: string): Promise<string> {
    const encoder = new TextEncoder()
    const keyData = encoder.encode(secret)
    const msgData = encoder.encode(payload)
    
    const key = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )
    
    const signature = await crypto.subtle.sign('HMAC', key, msgData)
    return Array.from(new Uint8Array(signature))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
  }

  async sendMessage(message: WebhookMessage, options: SendMessageOptions): Promise<SendMessageResult> {
    const { webhook, secret } = options

    if (!webhook) {
      return {
        success: false,
        error: "缺少 Webhook 地址"
      }
    }

    try {
      const { result, duration } = await this.measureDuration(async () => {
        let payload: string
        let contentType: string

        // 处理不同类型的消息格式
        if (message.data && typeof message.data === 'string') {
          // JSON 数据类型
          try {
            JSON.parse(message.data) // 验证是否为有效 JSON
            payload = message.data
            contentType = 'application/json'
          } catch {
            throw new Error('无效的 JSON 格式')
          }
        } else {
          // 表单数据类型
          payload = JSON.stringify(message)
          contentType = 'application/json'
        }

        const headers: Record<string, string> = {
          'Content-Type': contentType,
          'User-Agent': 'MoePush/2.0'
        }

        // 添加签名头
        if (secret) {
          const signature = await this.generateSignature(payload, secret)
          headers['X-Signature-SHA256'] = `sha256=${signature}`
        }

        const response = await fetch(webhook, {
          method: 'POST',
          headers,
          body: payload
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        let responseData
        try {
          responseData = await response.json()
        } catch {
          responseData = await response.text()
        }

        return responseData
      })

      return {
        success: true,
        message: "消息发送成功",
        response: result,
        duration
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || "发送失败",
        duration: error.duration
      }
    }
  }
}
