// 为了简化演示，我们使用内存存储
// 在实际生产环境中，应该使用 Cloudflare D1 或其他数据库
import * as schema from './schema'

// 模拟数据库存储
const memoryStore = {
  users: new Map(),
  channels: new Map(),
  endpoints: new Map(),
  endpointGroups: new Map(),
  endpointGroupMembers: new Map(),
  pushLogs: new Map(),
}

// 模拟数据库操作
export const db = {
  query: {
    endpoints: {
      findFirst: async (options: any) => {
        const endpoint = Array.from(memoryStore.endpoints.values()).find((e: any) =>
          options.where && e.id === options.where.value
        )
        if (endpoint && options.with?.channel) {
          const channel = memoryStore.channels.get((endpoint as any).channelId)
          return { ...endpoint, channel }
        }
        return endpoint
      }
    },
    endpointGroups: {
      findFirst: async (options: any) => {
        return Array.from(memoryStore.endpointGroups.values()).find((g: any) =>
          options.where && g.id === options.where.value
        )
      }
    },
    endpointGroupMembers: {
      findMany: async (options: any) => {
        const members = Array.from(memoryStore.endpointGroupMembers.values()).filter((m: any) =>
          options.where && m.groupId === options.where.value
        )
        if (options.with?.endpoint?.with?.channel) {
          return members.map((m: any) => {
            const endpoint = memoryStore.endpoints.get(m.endpointId)
            const channel = endpoint ? memoryStore.channels.get((endpoint as any).channelId) : null
            return { ...m, endpoint: endpoint ? { ...endpoint, channel } : null }
          })
        }
        return members
      }
    }
  },
  insert: (table: string) => ({
    values: async (data: any) => {
      const id = data.id || Math.random().toString(36).substring(2)
      const record = { ...data, id }

      if (table === 'pushLogs') {
        memoryStore.pushLogs.set(id, record)
      } else if (table === 'channels') {
        memoryStore.channels.set(id, record)
      } else if (table === 'endpoints') {
        memoryStore.endpoints.set(id, record)
      } else if (table === 'endpointGroups') {
        memoryStore.endpointGroups.set(id, record)
      } else if (table === 'endpointGroupMembers') {
        memoryStore.endpointGroupMembers.set(id, record)
      }

      return record
    }
  }),
  update: (table: string) => ({
    set: (data: any) => ({
      where: async (condition: any) => {
        const id = condition.value
        if (table === 'pushLogs' && memoryStore.pushLogs.has(id)) {
          const existing = memoryStore.pushLogs.get(id)
          memoryStore.pushLogs.set(id, { ...existing, ...data })
        }
        return true
      }
    })
  })
}

export function getDb() {
  return db
}

export { schema }
export type Database = typeof db
