import { BaseChannel, ChannelConfig, SendMessageOptions, SendMessageResult } from "./types"

interface WecomMessage {
  msgtype: string
  text?: { content: string; mentioned_list?: string[]; mentioned_mobile_list?: string[] }
  markdown?: { content: string }
  image?: { base64: string; md5: string }
  news?: { articles: Array<{ title: string; description?: string; url: string; picurl?: string }> }
}

export class WecomChannel extends BaseChannel {
  readonly config: ChannelConfig = {
    type: "wecom",
    label: "企业微信群机器人",
    description: "通过企业微信群机器人发送消息通知",
    configFields: [
      {
        key: "webhook",
        description: "Webhook 地址",
        placeholder: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx",
        required: true,
        component: 'input'
      }
    ],
    templates: [
      {
        type: "text",
        name: "文本消息",
        description: "发送文本消息",
        fields: [
          {
            key: "text.content",
            description: "消息内容",
            required: true,
            component: 'textarea'
          },
          {
            key: "text.mentioned_list",
            description: "提醒指定成员（@all 或用户ID，多个用逗号分隔）",
            component: 'input'
          },
          {
            key: "text.mentioned_mobile_list",
            description: "提醒指定成员手机号（多个用逗号分隔）",
            component: 'input'
          },
          {
            key: "msgtype",
            component: 'hidden',
            defaultValue: "text"
          }
        ]
      },
      {
        type: "markdown",
        name: "Markdown消息",
        description: "发送Markdown格式消息",
        fields: [
          {
            key: "markdown.content",
            description: "Markdown内容",
            required: true,
            component: 'textarea'
          },
          {
            key: "msgtype",
            component: 'hidden',
            defaultValue: "markdown"
          }
        ]
      }
    ]
  }

  async sendMessage(message: WecomMessage, options: SendMessageOptions): Promise<SendMessageResult> {
    const { webhook } = options

    if (!webhook) {
      return {
        success: false,
        error: "缺少 Webhook 地址"
      }
    }

    try {
      const { result, duration } = await this.measureDuration(async () => {
        // 处理 mentioned_list 和 mentioned_mobile_list 字符串转数组
        if (message.text?.mentioned_list && typeof message.text.mentioned_list === 'string') {
          message.text.mentioned_list = message.text.mentioned_list.split(',').map(s => s.trim()).filter(Boolean)
        }
        if (message.text?.mentioned_mobile_list && typeof message.text.mentioned_mobile_list === 'string') {
          message.text.mentioned_mobile_list = message.text.mentioned_mobile_list.split(',').map(s => s.trim()).filter(Boolean)
        }

        const response = await fetch(webhook, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(message)
        })

        const data = await response.json() as { errcode: number; errmsg: string }
        
        if (data.errcode !== 0) {
          throw new Error(`企业微信消息推送失败: ${data.errmsg}`)
        }

        return data
      })

      return {
        success: true,
        message: "消息发送成功",
        response: result,
        duration
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || "发送失败",
        duration: error.duration
      }
    }
  }
}
