import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string | number) {
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  }).format(new Date(date))
}

export function generateId() {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

export function safeJsonParse(str: string, fallback: any = null) {
  try {
    return JSON.parse(str)
  } catch {
    return fallback
  }
}

export function safeInterpolate(template: string, data: Record<string, any>): string {
  try {
    // 简单的模板插值，支持 ${key} 语法
    return template.replace(/\$\{([^}]+)\}/g, (match, key) => {
      const keys = key.split('.')
      let value = data
      
      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k]
        } else {
          return match // 如果找不到值，返回原始模板
        }
      }
      
      return typeof value === 'string' ? value : JSON.stringify(value)
    })
  } catch (error) {
    console.error('Template interpolation error:', error)
    return template
  }
}
