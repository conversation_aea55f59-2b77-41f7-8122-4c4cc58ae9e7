{"version": 3, "sources": ["../../src/sqlite-core/indexes.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { SQLiteColumn } from './columns/index.ts';\nimport type { SQLiteTable } from './table.ts';\n\nexport interface IndexConfig {\n\tname: string;\n\tcolumns: IndexColumn[];\n\tunique: boolean;\n\twhere: SQL | undefined;\n}\n\nexport type IndexColumn = SQLiteColumn | SQL;\n\nexport class IndexBuilderOn {\n\tstatic readonly [entityKind]: string = 'SQLiteIndexBuilderOn';\n\n\tconstructor(private name: string, private unique: boolean) {}\n\n\ton(...columns: [IndexColumn, ...IndexColumn[]]): IndexBuilder {\n\t\treturn new IndexBuilder(this.name, columns, this.unique);\n\t}\n}\n\nexport class IndexBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteIndexBuilder';\n\n\tdeclare _: {\n\t\tbrand: 'SQLiteIndexBuilder';\n\t};\n\n\t/** @internal */\n\tconfig: IndexConfig;\n\n\tconstructor(name: string, columns: IndexColumn[], unique: boolean) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tunique,\n\t\t\twhere: undefined,\n\t\t};\n\t}\n\n\t/**\n\t * Condition for partial index.\n\t */\n\twhere(condition: SQL): this {\n\t\tthis.config.where = condition;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: SQLiteTable): Index {\n\t\treturn new Index(this.config, table);\n\t}\n}\n\nexport class Index {\n\tstatic readonly [entityKind]: string = 'SQLiteIndex';\n\n\tdeclare _: {\n\t\tbrand: 'SQLiteIndex';\n\t};\n\n\treadonly config: IndexConfig & { table: SQLiteTable };\n\n\tconstructor(config: IndexConfig, table: SQLiteTable) {\n\t\tthis.config = { ...config, table };\n\t}\n}\n\nexport function index(name: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(name, false);\n}\n\nexport function uniqueIndex(name: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(name, true);\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAcpB,MAAM,eAAe;AAAA,EAG3B,YAAoB,MAAsB,QAAiB;AAAvC;AAAsB;AAAA,EAAkB;AAAA,EAF5D,QAAiB,UAAU,IAAY;AAAA,EAIvC,MAAM,SAAwD;AAC7D,WAAO,IAAI,aAAa,KAAK,MAAM,SAAS,KAAK,MAAM;AAAA,EACxD;AACD;AAEO,MAAM,aAAa;AAAA,EACzB,QAAiB,UAAU,IAAY;AAAA;AAAA,EAOvC;AAAA,EAEA,YAAY,MAAc,SAAwB,QAAiB;AAClE,SAAK,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,IACR;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAsB;AAC3B,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,OAA2B;AAChC,WAAO,IAAI,MAAM,KAAK,QAAQ,KAAK;AAAA,EACpC;AACD;AAEO,MAAM,MAAM;AAAA,EAClB,QAAiB,UAAU,IAAY;AAAA,EAM9B;AAAA,EAET,YAAY,QAAqB,OAAoB;AACpD,SAAK,SAAS,EAAE,GAAG,QAAQ,MAAM;AAAA,EAClC;AACD;AAEO,SAAS,MAAM,MAA8B;AACnD,SAAO,IAAI,eAAe,MAAM,KAAK;AACtC;AAEO,SAAS,YAAY,MAA8B;AACzD,SAAO,IAAI,eAAe,MAAM,IAAI;AACrC;", "names": []}