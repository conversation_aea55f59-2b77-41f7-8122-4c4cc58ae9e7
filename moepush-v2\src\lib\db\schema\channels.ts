import { sql } from "drizzle-orm"
import { text, sqliteTable, index } from "drizzle-orm/sqlite-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"
import { z } from "zod"
import { users } from "./users"

export const CHANNEL_TYPES = {
  DINGTALK: "dingtalk",
  WECOM: "wecom", 
  WECOM_APP: "wecom_app",
  TELEGRAM: "telegram",
  FEISHU: "feishu",
  DISCORD: "discord",
  BARK: "bark",
  WEBHOOK: "webhook",
} as const

export type ChannelType = typeof CHANNEL_TYPES[keyof typeof CHANNEL_TYPES]

export const channels = sqliteTable("channels", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type", { enum: Object.values(CHANNEL_TYPES) as [string, ...string[]] }).notNull(),
  webhook: text("webhook"),
  secret: text("secret"),
  corpId: text("corp_id"),
  agentId: text("agent_id"),
  botToken: text("bot_token"),
  chatId: text("chat_id"),
  status: text("status", { enum: ["active", "inactive"] }).notNull().default("active"),
  userId: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  createdAt: text("created_at").notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: text("updated_at").notNull().default(sql`CURRENT_TIMESTAMP`),
}, (table) => ({
  userIdIdx: index("channels_user_id_idx").on(table.userId),
  typeIdx: index("channels_type_idx").on(table.type),
}))

export const insertChannelSchema = createInsertSchema(channels).extend({
  name: z.string().min(1, "名称不能为空").max(50, "名称不能超过50个字符"),
  type: z.nativeEnum(CHANNEL_TYPES),
  webhook: z.string().url("请输入有效的URL").optional().or(z.literal("")),
  secret: z.string().optional(),
  corpId: z.string().optional(),
  agentId: z.string().optional(),
  botToken: z.string().optional(),
  chatId: z.string().optional(),
})

export const selectChannelSchema = createSelectSchema(channels)

export type Channel = typeof channels.$inferSelect
export type NewChannel = typeof channels.$inferInsert
export type ChannelFormData = z.infer<typeof insertChannelSchema>
