import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { getAvailableChannels } from "@/lib/channels"
import { MessageSquare, Zap, Shield, Globe } from "lucide-react"

export default function Home() {
  const channels = getAvailableChannels()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-full">
              <MessageSquare className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
            <PERSON><PERSON><PERSON> v2
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            一个现代化的消息推送服务，支持多种推送渠道，让消息触达变得简单高效 ✨
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              开始使用
            </Button>
            <Button variant="outline" size="lg">
              查看文档
            </Button>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <Card className="text-center">
            <CardHeader>
              <div className="flex justify-center mb-4">
                <Zap className="h-10 w-10 text-blue-500" />
              </div>
              <CardTitle>快速集成</CardTitle>
              <CardDescription>
                简单的 API 接口，几分钟即可完成集成
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="flex justify-center mb-4">
                <Globe className="h-10 w-10 text-green-500" />
              </div>
              <CardTitle>多渠道支持</CardTitle>
              <CardDescription>
                支持钉钉、企业微信、Telegram 等多种推送渠道
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="flex justify-center mb-4">
                <Shield className="h-10 w-10 text-purple-500" />
              </div>
              <CardTitle>安全可靠</CardTitle>
              <CardDescription>
                企业级安全保障，支持签名验证和权限控制
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Supported Channels */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
            支持的推送渠道
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {channels.map((channel) => (
              <Card key={channel.type} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg">{channel.label}</CardTitle>
                  <CardDescription className="text-sm">
                    {channel.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm text-gray-500">
                    {channel.templates.length} 个模板
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
            <CardHeader>
              <CardTitle className="text-2xl">准备开始了吗？</CardTitle>
              <CardDescription className="text-blue-100">
                立即创建你的第一个推送接口，体验便捷的消息推送服务
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="secondary" size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                立即开始
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
