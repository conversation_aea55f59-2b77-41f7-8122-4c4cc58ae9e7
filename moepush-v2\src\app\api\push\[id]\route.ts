import { NextRequest, NextResponse } from "next/server"
import { getDb } from "@/lib/db"
import { endpoints, channels } from "@/lib/db/schema"
import { pushLogs } from "@/lib/db/schema/push-logs"
import { eq } from "drizzle-orm"
import { safeInterpolate, generateId } from "@/lib/utils"
import { sendChannelMessage } from "@/lib/channels"
import { ChannelType } from "@/lib/db/schema/channels"

export const runtime = "edge"

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now()
  let logId: string | null = null

  try {
    const { id } = await params
    const db = getDb()

    // 查找端点和关联的渠道
    const endpoint = await db.query.endpoints.findFirst({
      where: eq(endpoints.id, id),
      with: {
        channel: true,
      },
    })

    if (!endpoint || !endpoint.channel) {
      return NextResponse.json(
        { success: false, error: "接口不存在" },
        { status: 404 }
      )
    }

    if (endpoint.status !== "active") {
      return NextResponse.json(
        { success: false, error: "接口已禁用" },
        { status: 403 }
      )
    }

    if (endpoint.channel.status !== "active") {
      return NextResponse.json(
        { success: false, error: "渠道已禁用" },
        { status: 403 }
      )
    }

    // 获取请求体
    const body = await request.json()
    
    // 创建推送日志
    logId = generateId()
    await db.insert('pushLogs').values({
      id: logId,
      endpointId: endpoint.id,
      userId: endpoint.userId,
      status: "pending",
      message: JSON.stringify(body),
      createdAt: new Date().toISOString(),
    })

    // 处理消息模板
    const processedTemplate = safeInterpolate(endpoint.rule, { body })
    let messageObj: any

    try {
      messageObj = JSON.parse(processedTemplate)
    } catch (error) {
      throw new Error("消息模板格式错误，请检查 JSON 格式")
    }

    // 发送消息
    const result = await sendChannelMessage(
      endpoint.channel.type as ChannelType,
      messageObj,
      {
        webhook: endpoint.channel.webhook || undefined,
        secret: endpoint.channel.secret || undefined,
        corpId: endpoint.channel.corpId || undefined,
        agentId: endpoint.channel.agentId || undefined,
        botToken: endpoint.channel.botToken || undefined,
        chatId: endpoint.channel.chatId || undefined,
      }
    )

    const duration = Date.now() - startTime

    // 更新推送日志
    await db.update(pushLogs)
      .set({
        status: result.success ? "success" : "failed",
        response: result.response ? JSON.stringify(result.response) : null,
        error: result.error || null,
        duration: result.duration || duration,
      })
      .where(eq(pushLogs.id, logId))

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message || "推送成功",
        duration: result.duration || duration,
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.error || "推送失败",
          duration: result.duration || duration,
        },
        { status: 500 }
      )
    }
  } catch (error: any) {
    const duration = Date.now() - startTime
    console.error("Push error:", error)

    // 更新推送日志（如果已创建）
    if (logId) {
      try {
        const db = getDb()
        await db.update(pushLogs)
          .set({
            status: "failed",
            error: error.message || "未知错误",
            duration,
          })
          .where(eq(pushLogs.id, logId))
      } catch (logError) {
        console.error("Failed to update push log:", logError)
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: error.message || "推送失败",
        duration,
      },
      { status: 500 }
    )
  }
}
