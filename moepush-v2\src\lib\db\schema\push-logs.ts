import { sql } from "drizzle-orm"
import { text, sqliteTable, index, integer } from "drizzle-orm/sqlite-core"
import { createInsertSchema, createSelectSchema } from "drizzle-zod"
import { users } from "./users"
import { endpoints } from "./endpoints"
import { endpointGroups } from "./endpoint-groups"

export const pushLogs = sqliteTable("push_logs", {
  id: text("id").primaryKey(),
  endpointId: text("endpoint_id").references(() => endpoints.id, { onDelete: "set null" }),
  groupId: text("group_id").references(() => endpointGroups.id, { onDelete: "set null" }),
  userId: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  status: text("status", { enum: ["success", "failed", "pending"] }).notNull(),
  message: text("message"), // Original message payload
  response: text("response"), // Response from the channel
  error: text("error"), // Error message if failed
  duration: integer("duration"), // Duration in milliseconds
  createdAt: text("created_at").notNull().default(sql`CURRENT_TIMESTAMP`),
}, (table) => ({
  userIdIdx: index("push_logs_user_id_idx").on(table.userId),
  endpointIdIdx: index("push_logs_endpoint_id_idx").on(table.endpointId),
  groupIdIdx: index("push_logs_group_id_idx").on(table.groupId),
  statusIdx: index("push_logs_status_idx").on(table.status),
  createdAtIdx: index("push_logs_created_at_idx").on(table.createdAt),
}))

export const insertPushLogSchema = createInsertSchema(pushLogs)
export const selectPushLogSchema = createSelectSchema(pushLogs)

export type PushLog = typeof pushLogs.$inferSelect
export type NewPushLog = typeof pushLogs.$inferInsert
