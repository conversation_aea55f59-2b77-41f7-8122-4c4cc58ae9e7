{"version": 3, "file": "mysql.d.ts", "sourceRoot": "", "sources": ["../src/lib/mysql.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAA4B,MAAM,aAAa,CAAA;AAC7E,OAAO,EACL,WAAW,EACX,aAAa,EAOb,oBAAoB,EACpB,qBAAqB,EACrB,mBAAmB,EACpB,MAAM,wBAAwB,CAAA;AAE/B,OAAO,KAAK,EACV,OAAO,EAOR,MAAM,qBAAqB,CAAA;AAG5B,wBAAgB,YAAY,CAC1B,MAAM,GAAE,OAAO,CAAC,kBAAkB,CAAM,GACvC,QAAQ,CAAC,kBAAkB,CAAC,CA2G9B;AAED,wBAAgB,mBAAmB,CACjC,MAAM,EAAE,aAAa,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,GAAG,CAAC,EACrE,MAAM,CAAC,EAAE,kBAAkB,GAC1B,OAAO,CA8OT;AAED,KAAK,iBAAiB,CACpB,CAAC,SAAS;IACR,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAA;IACtC,QAAQ,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,MAAM,CAAA;IAClD,OAAO,EAAE,OAAO,CAAA;IAChB,YAAY,CAAC,EAAE,OAAO,CAAA;IACtB,UAAU,EACN,cAAc,GACd,WAAW,GACX,cAAc,GACd,gBAAgB,GAChB,UAAU,CAAA;CACf,IACC,WAAW,CAAC;IACd,eAAe,EAAE,OAAO,CAAA;IACxB,YAAY,EAAE,CAAC,CAAC,cAAc,CAAC,SAAS,IAAI,GAAG,IAAI,GAAG,KAAK,CAAA;IAC3D,iBAAiB,EAAE,OAAO,CAAA;IAC1B,SAAS,EAAE,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,SAAS,CAAA;IACvD,IAAI,EAAE,MAAM,CAAA;IACZ,UAAU,EAAE,CAAC,CAAC,YAAY,CAAC,CAAA;IAC3B,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;IACf,WAAW,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,CAAA;IACtC,OAAO,EAAE,CAAC,CAAC,SAAS,CAAC,CAAA;IACrB,UAAU,EAAE,OAAO,CAAA;IACnB,UAAU,EAAE,GAAG,CAAA;IACf,QAAQ,EAAE,CAAC,CAAC,UAAU,CAAC,CAAA;IACvB,SAAS,EAAE,MAAM,CAAA;CAClB,CAAC,CAAA;AAEF,MAAM,MAAM,sBAAsB,GAAG,qBAAqB,CAAC;IACzD,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE;QACP,EAAE,EAAE,iBAAiB,CAAC;YACpB,YAAY,EAAE,IAAI,CAAA;YAClB,IAAI,EAAE,MAAM,CAAA;YACZ,QAAQ,EAAE,QAAQ,CAAA;YAClB,OAAO,EAAE,IAAI,CAAA;YACb,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;SACzC,CAAC,CAAA;QACF,IAAI,EAAE,iBAAiB,CAAC;YACtB,IAAI,EAAE,MAAM,CAAA;YACZ,QAAQ,EAAE,QAAQ,CAAA;YAClB,OAAO,EAAE,OAAO,CAAA;YAChB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;SACzC,CAAC,CAAA;QACF,KAAK,EAAE,iBAAiB,CAAC;YACvB,IAAI,EAAE,MAAM,CAAA;YACZ,QAAQ,EAAE,QAAQ,CAAA;YAClB,OAAO,EAAE,OAAO,CAAA;YAChB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;SACzC,CAAC,CAAA;QACF,aAAa,EAAE,iBAAiB,CAAC;YAC/B,IAAI,EAAE,IAAI,CAAA;YACV,QAAQ,EAAE,MAAM,CAAA;YAChB,OAAO,EAAE,OAAO,CAAA;YAChB,UAAU,EAAE,gBAAgB,CAAA;SAC7B,CAAC,CAAA;QACF,KAAK,EAAE,iBAAiB,CAAC;YACvB,IAAI,EAAE,MAAM,CAAA;YACZ,QAAQ,EAAE,QAAQ,CAAA;YAClB,OAAO,EAAE,OAAO,CAAA;YAChB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;SACzC,CAAC,CAAA;KACH,CAAA;IACD,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,CAAC,CAAA;AAEF,MAAM,MAAM,yBAAyB,GAAG,qBAAqB,CAAC;IAC5D,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE;QACP,MAAM,EAAE,iBAAiB,CAAC;YACxB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,IAAI,EAAE,iBAAiB,CAAC;YACtB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,QAAQ,EAAE,iBAAiB,CAAC;YAC1B,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,iBAAiB,EAAE,iBAAiB,CAAC;YACnC,QAAQ,EAAE,QAAQ,CAAA;YAClB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;SACd,CAAC,CAAA;QACF,aAAa,EAAE,iBAAiB,CAAC;YAC/B,QAAQ,EAAE,QAAQ,CAAA;YAClB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;SACjB,CAAC,CAAA;QACF,YAAY,EAAE,iBAAiB,CAAC;YAC9B,QAAQ,EAAE,QAAQ,CAAA;YAClB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,WAAW,EAAE,MAAM,GAAG,MAAM,CAAA;YAC5B,OAAO,EAAE,OAAO,CAAA;SACjB,CAAC,CAAA;QACF,UAAU,EAAE,iBAAiB,CAAC;YAC5B,QAAQ,EAAE,QAAQ,CAAA;YAClB,UAAU,EAAE,UAAU,CAAA;YACtB,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;SACjB,CAAC,CAAA;QACF,UAAU,EAAE,iBAAiB,CAAC;YAC5B,QAAQ,EAAE,QAAQ,CAAA;YAClB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;SACjB,CAAC,CAAA;QACF,KAAK,EAAE,iBAAiB,CAAC;YACvB,QAAQ,EAAE,QAAQ,CAAA;YAClB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;SACjB,CAAC,CAAA;QACF,QAAQ,EAAE,iBAAiB,CAAC;YAC1B,QAAQ,EAAE,QAAQ,CAAA;YAClB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;SACjB,CAAC,CAAA;QACF,aAAa,EAAE,iBAAiB,CAAC;YAC/B,QAAQ,EAAE,QAAQ,CAAA;YAClB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,OAAO,CAAA;SACjB,CAAC,CAAA;KACH,CAAA;IACD,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,CAAC,CAAA;AAEF,MAAM,MAAM,yBAAyB,GAAG,qBAAqB,CAAC;IAC5D,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE;QACP,YAAY,EAAE,iBAAiB,CAAC;YAC9B,YAAY,EAAE,IAAI,CAAA;YAClB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,MAAM,EAAE,iBAAiB,CAAC;YACxB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,OAAO,EAAE,iBAAiB,CAAC;YACzB,QAAQ,EAAE,MAAM,CAAA;YAChB,UAAU,EAAE,gBAAgB,CAAA;YAC5B,IAAI,EAAE,IAAI,CAAA;YACV,OAAO,EAAE,IAAI,CAAA;SACd,CAAC,CAAA;KACH,CAAA;IACD,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,CAAC,CAAA;AAEF,MAAM,MAAM,kCAAkC,GAAG,qBAAqB,CAAC;IACrE,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE;QACP,UAAU,EAAE,iBAAiB,CAAC;YAC5B,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,KAAK,EAAE,iBAAiB,CAAC;YACvB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,OAAO,EAAE,iBAAiB,CAAC;YACzB,QAAQ,EAAE,MAAM,CAAA;YAChB,UAAU,EAAE,gBAAgB,CAAA;YAC5B,IAAI,EAAE,IAAI,CAAA;YACV,OAAO,EAAE,IAAI,CAAA;SACd,CAAC,CAAA;KACH,CAAA;IACD,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,CAAC,CAAA;AAEF,MAAM,MAAM,8BAA8B,GAAG,qBAAqB,CAAC;IACjE,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE;QACP,YAAY,EAAE,iBAAiB,CAAC;YAC9B,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,MAAM,EAAE,iBAAiB,CAAC;YACxB,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,iBAAiB,EAAE,iBAAiB,CAAC;YACnC,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,mBAAmB,EAAE,iBAAiB,CAAC;YACrC,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,OAAO,EAAE,iBAAiB,CAAC;YACzB,UAAU,EAAE,UAAU,CAAA;YACtB,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,oBAAoB,EAAE,iBAAiB,CAAC;YACtC,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;QACF,kBAAkB,EAAE,iBAAiB,CAAC;YACpC,UAAU,EAAE,cAAc,CAAA;YAC1B,IAAI,EAAE,OAAO,CAAA;YACb,OAAO,EAAE,IAAI,CAAA;YACb,QAAQ,EAAE,SAAS,CAAA;SACpB,CAAC,CAAA;QACF,UAAU,EAAE,iBAAiB,CAAC;YAC5B,UAAU,EAAE,cAAc,GAAG,WAAW,CAAA;YACxC,IAAI,EAAE,MAAM,CAAA;YACZ,OAAO,EAAE,KAAK,CAAA;YACd,QAAQ,EAAE,QAAQ,CAAA;SACnB,CAAC,CAAA;KACH,CAAA;IACD,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,CAAC,CAAA;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B,UAAU,EAAE,sBAAsB,CAAA;IAClC,aAAa,EAAE,yBAAyB,CAAA;IACxC,aAAa,CAAC,EAAE,yBAAyB,CAAA;IACzC,uBAAuB,CAAC,EAAE,kCAAkC,CAAA;IAC5D,mBAAmB,CAAC,EAAE,8BAA8B,CAAA;CACrD,CAAA"}